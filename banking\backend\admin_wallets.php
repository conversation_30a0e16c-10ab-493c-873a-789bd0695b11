<?php
session_start();
require_once ('class.admin.php');
include_once ('session.php');
require_once ('../mod/SecurityValidator.php');

$reg_user = new USER();

if(!isset($_SESSION['email'])){
    header("Location: in.php");
    exit(); 
}

// Handle wallet updates
if(isset($_POST['btn-update'])) {
    // Validate and sanitize wallet addresses
    $btc_wallet = SecurityValidator::validateWalletAddress($_POST['btc_wallet'], 'BTC');
    $eth_wallet = SecurityValidator::validateWalletAddress($_POST['eth_wallet'], 'ETH');
    $usdt_wallet = SecurityValidator::validateWalletAddress($_POST['usdt_wallet'], 'USDT');
    
    if (!$btc_wallet && !$eth_wallet && !$usdt_wallet) {
        $msg = "<div class='alert alert-warning'>
                <strong>WARNING!</strong> At least one valid wallet address is required!
                </div>";
        SecurityValidator::logSecurityEvent('No valid admin wallet addresses provided', 'WARNING');
    } else {
        // Use empty string for invalid addresses instead of false
        $btc_wallet = $btc_wallet ?: '';
        $eth_wallet = $eth_wallet ?: '';
        $usdt_wallet = $usdt_wallet ?: '';
        
        // Update admin wallet settings in bk_settings table (using the correct column names)
        $stmt = $reg_user->runQuery("UPDATE bk_settings SET
                                    btc_wallet = :btc_wallet,
                                    eth_wallet = :eth_wallet,
                                    usdt_wallet = :usdt_wallet
                                    WHERE id = '1'");
        
        if($stmt->execute(array(
            ":btc_wallet" => $btc_wallet,
            ":eth_wallet" => $eth_wallet,
            ":usdt_wallet" => $usdt_wallet
        ))) {
            $msg = "<div class='alert alert-success'>
                    <strong>SUCCESS!</strong> Admin wallet addresses updated successfully!
                    </div>";
            SecurityValidator::logSecurityEvent('Admin wallet addresses updated successfully', 'INFO', [
                'btc_wallet' => !empty($btc_wallet),
                'eth_wallet' => !empty($eth_wallet),
                'usdt_wallet' => !empty($usdt_wallet)
            ]);
        } else {
            $msg = "<div class='alert alert-danger'>
                    <strong>ERROR!</strong> Failed to update admin wallet addresses!
                    </div>";
            SecurityValidator::logSecurityEvent('Failed to update admin wallet addresses', 'ERROR');
        }
    }
}

// Get current admin wallet settings
$stmt = $reg_user->runQuery("SELECT * FROM bk_settings WHERE id = '1'");
$stmt->execute();
$settings = $stmt->fetch(PDO::FETCH_ASSOC);

$admin_btc_wallet = $settings['btc_wallet'] ?? '';
$admin_eth_wallet = $settings['eth_wallet'] ?? '';
$admin_usdt_wallet = $settings['usdt_wallet'] ?? '';
?>

<?php
include 'dbconnect.php';

$sql = "SELECT * FROM account ORDER BY id";
$sql1 = "SELECT * FROM ticket ";
$sql2 = "SELECT * FROM transfer";
$sql3 = "SELECT * FROM account WHERE verify ='N' ORDER BY id DESC LIMIT 200";

if ($result = mysqli_query($connection, $sql)) {
    $rowcount = mysqli_num_rows($result);
    mysqli_free_result($result);

    if ($result1 = mysqli_query($connection, $sql1)) {
        $rowcount1 = mysqli_num_rows($result1);
        mysqli_free_result($result1);

        if ($result2 = mysqli_query($connection, $sql2)) {
            $rowcount2 = mysqli_num_rows($result2);
            mysqli_free_result($result2);

            if ($result3 = mysqli_query($connection, $sql3)) {
                $rowcount3 = mysqli_num_rows($result3);
                mysqli_free_result($result3);
            }
        }
    }
}

mysqli_close($connection);
?>

<?php include 'headeradmin.php'; ?>

<!---begin of Mobile View Here   only from Domain Team-->
<?php include 'menu.php'; ?>
<!---End of Mobile View Here   only from Domain Team-->

<script type="text/javascript" src="paginator.js"></script>
<script type="text/javascript" src="table.js"></script>

<div class="container-fluid mt--7">
    <!-- Admin Wallet Management -->
    <div class="row">
        <div class="col">
            <div class="card shadow">
                <div class="card-header border-0">
                    <h3 class="mb-0"><a href="" class="btn btn-danger">ADMIN WALLET MANAGEMENT</a></h3>
                </div>
                
                <div class="card-body">
                    
                    <?php
                    if(isset($msg)) {
                        echo $msg;
                    }
                    ?>
                    
                    <div class="alert alert-info">
                        <strong>Instructions:</strong>
                        <ul>
                            <li>These are the main admin wallet addresses that customers will send deposits to</li>
                            <li>BTC addresses should start with 1, 3, or bc1</li>
                            <li>ETH/USDT addresses should start with 0x and be 42 characters long</li>
                            <li>Make sure you control these wallet addresses before setting them</li>
                            <li>These addresses will be shown to customers during deposit process</li>
                        </ul>
                    </div>
                    
                    <div class="container-fluid">
                        <form method='post' class=''>
                            
                            <div class="row">
                                <div class="form-group col-md-4">
                                    <label for="btc_wallet">ADMIN BTC WALLET ADDRESS</label>
                                    <small class="form-text text-muted">Bitcoin wallet address (starts with 1, 3, or bc1)</small>
                                    <input id="btc_wallet" name="btc_wallet" class="form-control" type="text" 
                                           value="<?php echo SecurityValidator::sanitizeOutput($admin_btc_wallet); ?>" 
                                           placeholder="**********************************" />
                                </div>
                                
                                <div class="form-group col-md-4">
                                    <label for="eth_wallet">ADMIN ETH WALLET ADDRESS</label>
                                    <small class="form-text text-muted">Ethereum wallet address (starts with 0x)</small>
                                    <input id="eth_wallet" name="eth_wallet" class="form-control" type="text" 
                                           value="<?php echo SecurityValidator::sanitizeOutput($admin_eth_wallet); ?>" 
                                           placeholder="******************************************" />
                                </div>
                                
                                <div class="form-group col-md-4">
                                    <label for="usdt_wallet">ADMIN USDT WALLET ADDRESS</label>
                                    <small class="form-text text-muted">USDT wallet address (starts with 0x)</small>
                                    <input id="usdt_wallet" name="usdt_wallet" class="form-control" type="text" 
                                           value="<?php echo SecurityValidator::sanitizeOutput($admin_usdt_wallet); ?>" 
                                           placeholder="******************************************" />
                                </div>
                            </div>
                            
                            <div class="clearfix"></div>
                            <br />
                            
                            <table>
                                <tr>
                                    <td colspan="2">
                                        <button type="submit" class="btn btn-primary" name="btn-update">
                                            <span class="glyphicon glyphicon-edit"></span> Update Admin Wallets
                                        </button>
                                        <a href="index.php" class="btn btn-large btn-success">
                                            <i class="glyphicon glyphicon-backward"></i> &nbsp; BACK TO DASHBOARD
                                        </a>
                                    </td>
                                </tr>
                            </table>
                        </form>
                        
                        <!-- Current Wallet Status -->
                        <div class="mt-4">
                            <h5>Current Admin Wallet Status</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="card <?php echo !empty($admin_btc_wallet) ? 'bg-gradient-success' : 'bg-gradient-secondary'; ?>">
                                        <div class="card-body text-white">
                                            <h6>BTC Wallet</h6>
                                            <p><?php echo !empty($admin_btc_wallet) ? 'Configured' : 'Not Set'; ?></p>
                                            <?php if(!empty($admin_btc_wallet)): ?>
                                                <small><?php echo substr($admin_btc_wallet, 0, 10) . '...'; ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card <?php echo !empty($admin_eth_wallet) ? 'bg-gradient-success' : 'bg-gradient-secondary'; ?>">
                                        <div class="card-body text-white">
                                            <h6>ETH Wallet</h6>
                                            <p><?php echo !empty($admin_eth_wallet) ? 'Configured' : 'Not Set'; ?></p>
                                            <?php if(!empty($admin_eth_wallet)): ?>
                                                <small><?php echo substr($admin_eth_wallet, 0, 10) . '...'; ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card <?php echo !empty($admin_usdt_wallet) ? 'bg-gradient-success' : 'bg-gradient-secondary'; ?>">
                                        <div class="card-body text-white">
                                            <h6>USDT Wallet</h6>
                                            <p><?php echo !empty($admin_usdt_wallet) ? 'Configured' : 'Not Set'; ?></p>
                                            <?php if(!empty($admin_usdt_wallet)): ?>
                                                <small><?php echo substr($admin_usdt_wallet, 0, 10) . '...'; ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'foot.php'; ?>
