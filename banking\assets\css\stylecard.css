body{
  background: #62809a;
}
h1{
  text-align: center;
  font-family: monospace;
  color: white;
}
.form-cc {
    margin: 20px auto;
    position: relative;
    max-width: 460px;
}

.form-cc > div.card {
  border: 2px solid silver;
  width: 76%;
  max-width: 310px;
  border-radius: 10px;
  padding: 20px 24px 24px;
  height: 183px;
}

.form-cc > div.card-front {
  position: relative;
  background-color: #ebebeb;
  z-index: 20;
  box-shadow: 3px 3px 3px rgba(0,0,0,.1);
}

.form-cc > div.card-back {
  position: absolute;
  top: 30px;
  right: 0;
  background-color: #e0e0e0;
  text-align: right;
}

.card-front-icons {
  min-height: 22px;
  text-align: right;
  margin-bottom: 13px;
}

.card-front-icons > img {
  width: 34px;
  height: 22px;
  margin-left: 8px;
}

.form-cc > div > div.card-exp {
  display: flex;
  flex-direction: row;
  float: right;
  font-size: 18px;
}

.form-cc > div > div.card-exp > label {
  width: 50px;
  text-transform: uppercase;
  font-family: monospace;
  font-size: 13px;
  font-weight: 100;
  text-align: right;
  margin-top: 4px;
}

.form-cc > div > div.card-exp > input {
  display: block;
  box-sizing: border-box;
  width: 70px;
  padding: 0px 18px;
  margin-left: 10px;
  text-align: center;
}

.form-cc > div > div.card-exp > span {
  font-size: 30px;
  margin-left: 10px;
  font-weight: 600;
  color:  #7b7b7b;
}

.form-cc > div > div > input {
  box-sizing: border-box;
  height: 40px;
  width: 100%;
  margin-bottom: 12px;
  padding: 0px 13px;
  border-radius: 3px;
  border: solid 1px #bbc3c5;
  outline: 0 none;
  font-weight: 400;
  font-size: 17px;
  line-height: 1.65;
  font-family: monospace;
  text-transform: uppercase;
}

.form-cc > div.card-back > div.cvc {
    width: 64px;
    position: absolute;
    top: 107px;
    right: 21px;
    text-align: center;
    font-size: 30px;
}

.form-cc > div.card-back > div.cvc > input {
  text-align: center;
}


.form-cc > div > div > input:focus {
    border: 1px solid #f70!important;
}