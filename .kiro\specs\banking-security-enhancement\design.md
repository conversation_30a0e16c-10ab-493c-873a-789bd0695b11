# Design Document

## Overview

This design addresses critical security vulnerabilities in the BTC deposit system and implements comprehensive admin wallet management functionality. The solution focuses on security hardening, UI/UX consistency, and administrative control over cryptocurrency wallet configurations.

## Architecture

### Security Layer Architecture
- **Input Validation Layer**: Sanitizes all user inputs using PHP filter functions and prepared statements
- **Output Encoding Layer**: Escapes all output using htmlspecialchars() and context-appropriate encoding
- **Authentication Layer**: Validates session integrity and user permissions
- **Audit Layer**: Logs all wallet management activities and security events

### Database Schema Extensions
```sql
-- New table for admin wallet management
CREATE TABLE admin_wallets (
    id INT PRIMARY KEY AUTO_INCREMENT,
    currency_type ENUM('BTC', 'ETH', 'USDT') NOT NULL,
    wallet_address VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by VARCHAR(100) NOT NULL
);

-- Add wallet columns to bk_settings table
ALTER TABLE bk_settings ADD COLUMN btc_wallet VARCHAR(255);
ALTER TABLE bk_settings ADD COLUMN eth_wallet VARCHAR(255);
ALTER TABLE bk_settings ADD COLUMN usdt_wallet VARCHAR(255);
```

## Components and Interfaces

### 1. Security Hardening Components

#### Input Sanitization Class
```php
class SecurityValidator {
    public static function sanitizeInput($input, $type = 'string')
    public static function validateCryptoAddress($address, $currency)
    public static function escapeOutput($data)
    public static function logSecurityEvent($event, $details)
}
```

#### BTC Deposit Security Wrapper
- Wraps all BTC deposit functionality with security validation
- Implements CSRF protection tokens
- Validates all form inputs before processing
- Sanitizes wallet addresses and amounts

### 2. UI/UX Standardization Components

#### Consistent Styling Framework
- Extends existing Bootstrap-based design system
- Maintains color scheme: `#28a745` (success), `#dc3545` (danger), `#007bff` (primary)
- Uses Material Design card components with consistent shadows and borders
- Implements responsive grid system matching other dashboard pages

#### Form Component Library
- Standardized input fields with consistent validation styling
- Unified button styling with hover effects
- Consistent error message display patterns
- Mobile-responsive form layouts

### 3. Admin Wallet Management Components

#### Admin Navigation Extension
```php
// Add to admin menu structure
<li class="nav-item">
    <a class="nav-link" href="admin_wallets.php">
        <i class="fas fa-wallet"></i> Admin Wallets
    </a>
</li>
```

#### Wallet Management Interface
- CRUD operations for wallet addresses
- Real-time address validation
- Currency-specific input fields
- Audit trail display

#### Wallet Configuration API
```php
class WalletManager {
    public function updateWallet($currency, $address, $adminId)
    public function getActiveWallet($currency)
    public function validateAddress($address, $currency)
    public function getWalletHistory($currency)
}
```

## Data Models

### Admin Wallet Model
```php
class AdminWallet {
    private $id;
    private $currency_type; // BTC, ETH, USDT
    private $wallet_address;
    private $is_active;
    private $created_at;
    private $updated_at;
    private $updated_by;
}
```

### Security Event Model
```php
class SecurityEvent {
    private $id;
    private $event_type; // 'wallet_update', 'security_violation', 'admin_access'
    private $user_id;
    private $ip_address;
    private $details;
    private $timestamp;
}
```

## Error Handling

### Security Error Handling
- **Invalid Input**: Log security event, display generic error message
- **Injection Attempts**: Block request, log detailed security event, notify administrators
- **Authentication Failures**: Implement rate limiting, log attempts
- **Wallet Validation Errors**: Display specific validation messages for each currency type

### User Experience Error Handling
- **Form Validation**: Real-time client-side validation with server-side verification
- **Network Errors**: Graceful degradation with retry mechanisms
- **Session Timeouts**: Automatic redirect to login with session restoration

## Testing Strategy

### Security Testing
1. **Penetration Testing**: SQL injection, XSS, CSRF attacks on all new components
2. **Input Validation Testing**: Boundary testing for all form inputs
3. **Authentication Testing**: Session hijacking and privilege escalation attempts
4. **Wallet Address Testing**: Invalid address format testing for each currency

### Functional Testing
1. **Admin Wallet CRUD**: Create, read, update, delete operations
2. **UI Consistency**: Visual regression testing across all pages
3. **Responsive Design**: Cross-device and cross-browser testing
4. **Integration Testing**: End-to-end deposit flow testing

### Performance Testing
1. **Database Query Optimization**: Ensure wallet queries are efficient
2. **Page Load Testing**: Verify new components don't impact performance
3. **Concurrent User Testing**: Multiple admin users managing wallets simultaneously

## Implementation Phases

### Phase 1: Security Hardening (Priority: Critical)
- Implement input sanitization across all BTC deposit components
- Add CSRF protection to all forms
- Create security logging infrastructure
- Audit and fix existing vulnerabilities

### Phase 2: UI/UX Standardization (Priority: High)
- Redesign BTC deposit page to match dashboard styling
- Implement consistent form components
- Ensure responsive design compliance
- Update navigation and layout elements

### Phase 3: Admin Wallet Management (Priority: High)
- Create admin wallet management interface
- Implement wallet CRUD operations
- Add multi-currency support (BTC, ETH, USDT)
- Implement address validation for each currency type

### Phase 4: Integration and Testing (Priority: Medium)
- Integrate all components
- Comprehensive security testing
- Performance optimization
- User acceptance testing