-- Add admin wallet columns to bk_settings table
-- This script adds the necessary columns for admin wallet management

-- Add admin wallet address columns to bk_settings table
ALTER TABLE `bk_settings` 
ADD COLUMN `admin_btc_wallet` VARCHAR(255) DEFAULT '' COMMENT 'Admin Bitcoin wallet address',
ADD COLUMN `admin_eth_wallet` VARCHAR(255) DEFAULT '' COMMENT 'Admin Ethereum wallet address', 
ADD COLUMN `admin_usdt_wallet` VARCHAR(255) DEFAULT '' COMMENT 'Admin USDT wallet address';

-- Update the existing record with empty values (admin can set them later)
UPDATE `bk_settings` SET 
    `admin_btc_wallet` = '',
    `admin_eth_wallet` = '',
    `admin_usdt_wallet` = ''
WHERE `id` = 1;

-- Verify the changes
SELECT * FROM `bk_settings` WHERE `id` = 1;
