# Project Structure

## Root Directory Layout
```
banking/
├── assets/           # Frontend assets (CSS, JS, images)
├── backend/          # Admin panel and core classes
├── SQL/              # Database schema and setup
├── icon/             # Brand assets and media files
├── mod/              # Core modules (session, controller)
└── *.php             # Customer-facing pages
```

## Key Directories

### `/banking/` (Customer Interface)
- Authentication pages: `auth_*.php`
- Main application files for customer banking operations
- Session management and 2FA verification flows

### `/banking/backend/` (Admin Interface)
- `class.admin.php` - Core USER class with all database operations
- `connectdb.php` - Database configuration and site settings
- `index.php` - Admin dashboard
- Admin management pages: `edit-*.php`, `delete-*.php`
- `pic/` - Customer profile image uploads

### `/banking/assets/` 
- `css/` - Stylesheets including auth.css, material design
- `js/` - JavaScript libraries (jQuery, Chart.js, custom scripts)

## File Naming Conventions
- Authentication pages: `auth_[feature].php`
- Admin pages: `[action]-[entity].php` (e.g., `edit-customer.php`)
- Code verification: `auth_code_[number].php`
- Classes: `class.[name].php`

## Database Integration
- All database operations go through the USER class in `class.admin.php`
- Use prepared statements for all queries
- Session data stored in `$_SESSION` with proper validation

## Security Patterns
- Include session checks: `include_once ('mod/session.php')`
- Sanitize inputs: `trim()`, `strip_tags()`, `htmlspecialchars()`
- Redirect unauthorized users: `header("Location: ps_access.php")`
- Validate authentication codes before processing transactions