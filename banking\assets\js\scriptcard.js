//INI-ELEMENT
        var card = {
          nb:document.getElementsByName('nb')[0],
          mm:document.getElementsByName('mm')[0],
          yy:document.getElementsByName('yy')[0],
          ch:document.getElementsByName('ch')[0],
          cvc:document.getElementsByName('cvc')[0],

          styles:{
            valid:`
              border: 1px solid rgb(76, 175, 80)!important;
              background: rgba(76, 175, 80, 0.06)!important;`,
            error:`
              border: 1px solid #F44336!important;
              background: #f4433647!important;`,
            default:`
              border: solid 1px #bbc3c5;
              background: white!important;`
          }
        };
        //card.nb
        card.nb.oninput = function() {
          validate_card_num();
        }
        card.nb.addEventListener('keypress', e => {
          if(!/\d/.test(e.key))
            e.preventDefault();
          if (card.nb.value.length == 4  || card.nb.value.length == 9 ||
              card.nb.value.length == 14 || card.nb.value.length == 19) {
            card.nb.value += ' ';
          }
          validate_card_num();
        });
        function validate_card_num() {
          if(card.nb.value.length >= 19) {
            if(valid_credit_card(card.nb.value)) {
              card.nb.style.cssText  = card.styles.valid;
              card.mm.focus();
            } else {
              card.nb.style.cssText  = card.styles.error;
            }
          } else {
            card.nb.style.cssText  = card.styles.default;
          }
        }
        //card.mm
        card.mm.addEventListener('keypress', e => {
          if (!/\d/.test(e.key)) e.preventDefault();
        });
        card.mm.addEventListener('keyup', e => {
          if (Number(card.mm.value.length) >=2) {
            if (Number(card.mm.value) > 0 && Number(card.mm.value) <= 12) {
              card.mm.style.cssText  = card.styles.valid;
              card.yy.focus();
            } else {
              card.mm.style.cssText  = card.styles.error;
            }
          } else {
            card.mm.style.cssText  = card.styles.default;
          }
        });
        //card.yy
        card.yy.addEventListener('keypress', e => {
          if (!/\d/.test(e.key)) e.preventDefault();
        });
        card.yy.addEventListener('keyup', e => {
          if (Number(card.yy.value.length) >=2) {
            if (Number(card.yy.value) >= 24 && Number(card.yy.value) <= 38) {
              card.yy.style.cssText  = card.styles.valid;
              card.ch.focus();
            } else {
              card.yy.style.cssText  = card.styles.error;
            }
          } else {
            card.yy.style.cssText  = card.styles.default;
          }
        });
        //card.yy
        card.ch.addEventListener('keyup', e => {
          if (card.ch.value.length >= 1) {
            card.ch.style.cssText = card.styles.valid;
          } else {
            card.ch.style.cssText = card.styles.error;
          }
        });
        //card.cvc
        card.cvc.addEventListener('keypress', e => {
          if (!/\d/.test(e.key)) e.preventDefault();
        });
        card.cvc.addEventListener('keyup', e => {
          if (card.cvc.value.length >= 3) {
            card.cvc.style.cssText = card.styles.valid;
          } else {
            card.cvc.style.cssText = card.styles.error;
          }
        });
        //FUNCTION
        /* Luhn algorithm */
        function valid_credit_card(value) {
          // accept only digits, dashes or spaces
        	if (/[^0-9-\s]+/.test(value)) return false;

        	// The Luhn Algorithm. It's so pretty.
        	var nCheck = 0, nDigit = 0, bEven = false;
        	value = value.replace(/\D/g, "");

        	for (var n = value.length - 1; n >= 0; n--) {
        		var cDigit = value.charAt(n),
        			  nDigit = parseInt(cDigit, 10);

        		if (bEven) {
        			if ((nDigit *= 2) > 9) nDigit -= 9;
        		}

        		nCheck += nDigit;
        		bEven = !bEven;
        	}

        	return (nCheck % 10) == 0;
        }