<?php
session_start();
include_once ('mod/session.php');
require_once 'mod/controller.php';

if (!isset($_SESSION['cs_uname'])){
    header("Location: ps_access.php");
    exit();
}

$reg_user = new USER();

// Test database connection and wallet retrieval
echo "<h2>Wallet Debug Information</h2>";

// Check what columns exist in bk_settings table
$stmt = $reg_user->runQuery("DESCRIBE bk_settings");
$stmt->execute();
$columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<h3>Available columns in bk_settings table:</h3>";
echo "<ul>";
foreach($columns as $column) {
    echo "<li>" . $column['Field'] . " (" . $column['Type'] . ")</li>";
}
echo "</ul>";

// Try to get wallet settings with the user's column names
echo "<h3>Trying to get wallet settings with user's column names (btc_wallet, eth_wallet, usdt_wallet):</h3>";
try {
    $stmt_wallet = $reg_user->runQuery("SELECT btc_wallet, eth_wallet, usdt_wallet FROM bk_settings WHERE id = '1'");
    $stmt_wallet->execute();
    $wallet_settings = $stmt_wallet->fetch(PDO::FETCH_ASSOC);
    
    echo "<pre>";
    print_r($wallet_settings);
    echo "</pre>";
    
    echo "<p><strong>BTC Wallet:</strong> " . ($wallet_settings['btc_wallet'] ?? 'NOT SET') . "</p>";
    echo "<p><strong>ETH Wallet:</strong> " . ($wallet_settings['eth_wallet'] ?? 'NOT SET') . "</p>";
    echo "<p><strong>USDT Wallet:</strong> " . ($wallet_settings['usdt_wallet'] ?? 'NOT SET') . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error with user's column names: " . $e->getMessage() . "</p>";
}

// Try to get wallet settings with the original column names
echo "<h3>Trying to get wallet settings with original column names (admin_btc_wallet, admin_eth_wallet, admin_usdt_wallet):</h3>";
try {
    $stmt_wallet2 = $reg_user->runQuery("SELECT admin_btc_wallet, admin_eth_wallet, admin_usdt_wallet FROM bk_settings WHERE id = '1'");
    $stmt_wallet2->execute();
    $wallet_settings2 = $stmt_wallet2->fetch(PDO::FETCH_ASSOC);
    
    echo "<pre>";
    print_r($wallet_settings2);
    echo "</pre>";
    
    echo "<p><strong>Admin BTC Wallet:</strong> " . ($wallet_settings2['admin_btc_wallet'] ?? 'NOT SET') . "</p>";
    echo "<p><strong>Admin ETH Wallet:</strong> " . ($wallet_settings2['admin_eth_wallet'] ?? 'NOT SET') . "</p>";
    echo "<p><strong>Admin USDT Wallet:</strong> " . ($wallet_settings2['admin_usdt_wallet'] ?? 'NOT SET') . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error with original column names: " . $e->getMessage() . "</p>";
}

// Show all data from bk_settings
echo "<h3>All data from bk_settings table:</h3>";
try {
    $stmt_all = $reg_user->runQuery("SELECT * FROM bk_settings WHERE id = '1'");
    $stmt_all->execute();
    $all_settings = $stmt_all->fetch(PDO::FETCH_ASSOC);
    
    echo "<pre>";
    print_r($all_settings);
    echo "</pre>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error getting all settings: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='btc_deposit.php'>Go back to BTC Deposit</a></p>";
echo "<p><a href='backend/admin_wallets.php'>Go to Admin Wallets</a></p>";
?>
