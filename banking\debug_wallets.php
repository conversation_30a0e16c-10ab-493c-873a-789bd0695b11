<?php
session_start();
include_once ('mod/session.php');
require_once 'mod/controller.php';

if (!isset($_SESSION['cs_uname'])){
    header("Location: ps_access.php");
    exit();
}

$reg_user = new USER();

// Test database connection and wallet retrieval
echo "<h2>Wallet Debug Information</h2>";

// Check what columns exist in bk_settings table
$stmt = $reg_user->runQuery("DESCRIBE bk_settings");
$stmt->execute();
$columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<h3>Available columns in bk_settings table:</h3>";
echo "<ul>";
foreach($columns as $column) {
    echo "<li>" . $column['Field'] . " (" . $column['Type'] . ")</li>";
}
echo "</ul>";

// Try to get wallet settings with the correct column names
echo "<h3>Getting wallet settings with correct column names (admin_btc_wallet, admin_eth_wallet, admin_usdt_wallet):</h3>";
try {
    $stmt_wallet = $reg_user->runQuery("SELECT admin_btc_wallet, admin_eth_wallet, admin_usdt_wallet FROM bk_settings WHERE id = '1'");
    $stmt_wallet->execute();
    $wallet_settings = $stmt_wallet->fetch(PDO::FETCH_ASSOC);

    echo "<pre>";
    print_r($wallet_settings);
    echo "</pre>";

    echo "<p><strong>Admin BTC Wallet:</strong> " . ($wallet_settings['admin_btc_wallet'] ?? 'NOT SET') . "</p>";
    echo "<p><strong>Admin ETH Wallet:</strong> " . ($wallet_settings['admin_eth_wallet'] ?? 'NOT SET') . "</p>";
    echo "<p><strong>Admin USDT Wallet:</strong> " . ($wallet_settings['admin_usdt_wallet'] ?? 'NOT SET') . "</p>";

    // Show which wallets are configured
    $configured_wallets = [];
    if (!empty($wallet_settings['admin_btc_wallet'])) $configured_wallets[] = 'BTC';
    if (!empty($wallet_settings['admin_eth_wallet'])) $configured_wallets[] = 'ETH';
    if (!empty($wallet_settings['admin_usdt_wallet'])) $configured_wallets[] = 'USDT';

    echo "<p><strong>Configured Wallets:</strong> " . (empty($configured_wallets) ? 'NONE' : implode(', ', $configured_wallets)) . "</p>";

} catch (Exception $e) {
    echo "<p style='color: red;'>Error getting wallet settings: " . $e->getMessage() . "</p>";
}

// Show all data from bk_settings
echo "<h3>All data from bk_settings table:</h3>";
try {
    $stmt_all = $reg_user->runQuery("SELECT * FROM bk_settings WHERE id = '1'");
    $stmt_all->execute();
    $all_settings = $stmt_all->fetch(PDO::FETCH_ASSOC);
    
    echo "<pre>";
    print_r($all_settings);
    echo "</pre>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error getting all settings: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='btc_deposit.php'>Go back to BTC Deposit</a></p>";
echo "<p><a href='backend/admin_wallets.php'>Go to Admin Wallets</a></p>";
?>
