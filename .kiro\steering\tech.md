# Technology Stack

## Backend
- **Language**: PHP 7.x+
- **Database**: MySQL/MariaDB with PDO
- **Architecture**: MVC pattern with custom classes
- **Email**: PHPMailer with SMTP (Hostinger)
- **SMS**: Twilio integration for OTP/alerts

## Frontend
- **Framework**: Bootstrap-based responsive design
- **JavaScript**: jQuery, Chart.js for analytics
- **CSS**: Custom styling with Material Design elements
- **Authentication**: Session-based with encryption (AES)

## Database Structure
- Main tables: `account`, `transfer`, `alerts`, `ticket`, `admin`, `bk_settings`
- Uses prepared statements for security
- Supports multiple currencies and account types

## Security Features
- Password hashing with MD5 (consider upgrading to bcrypt)
- SQL injection protection via PDO prepared statements
- Session management for authentication
- 2FA PIN verification system
- Input sanitization and validation

## Common Commands
- **Database**: Access via phpMyAdmin or MySQL CLI
- **Email Testing**: Check SMTP configuration in `class.admin.php`
- **File Uploads**: Profile pictures stored in `backend/pic/`
- **Logs**: Check `backend/error_log` for PHP errors

## Configuration Files
- `backend/connectdb.php` - Database and site settings
- `mod/controller.php` - Main application controller
- `mod/session.php` - Session management