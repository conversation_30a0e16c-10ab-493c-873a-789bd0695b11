<?php
session_start();
include_once ('mod/session.php');
require_once 'mod/controller.php';
require_once 'mod/SecurityValidator.php';

if (!isset($_SESSION['cs_uname'])){

header("Location: ps_access.php");
exit();
}

// Validate session integrity
if (!SecurityValidator::validateSession()) {
    SecurityValidator::logSecurityEvent('Invalid session detected in BTC deposit', 'WARNING');
    header("Location: ps_access.php");
    exit();
}

$reg_user = new USER();

$stmt = $reg_user->runQuery("SELECT * FROM account WHERE cs_uname=:cs_uname");
$stmt->execute(array(":cs_uname" => $_SESSION['cs_uname']));
$row = $stmt->fetch(PDO::FETCH_ASSOC);

$email = $row['email'];

// Fixed SQL injection vulnerability - use prepared statement
$temp = $reg_user->runQuery("SELECT * FROM transfer WHERE email = :email");
$temp->execute(array(":email" => $email));
$rows = $temp->fetch(PDO::FETCH_ASSOC);

if (isset($_POST['login'])) {
   // Validate and sanitize inputs
   $name = SecurityValidator::validateWalletAddress($_POST['name'], 'BTC');
   $amount = SecurityValidator::validateAmount($_POST['amount'], 50, 100000);
   $email = SecurityValidator::validateEmail($_POST['email']);
   $accid = SecurityValidator::validateUserId($_POST['cs_id']);
   $accname = SecurityValidator::validateText($_POST['acc_name'], 100);

   // Check if all validations passed
   if (!$name || !$amount || !$email || !$accid || !$accname) {
       SecurityValidator::logSecurityEvent('Input validation failed in BTC deposit', 'WARNING', [
           'name_valid' => (bool)$name,
           'amount_valid' => (bool)$amount,
           'email_valid' => (bool)$email,
           'accid_valid' => (bool)$accid,
           'accname_valid' => (bool)$accname
       ]);
       header("Location: ps_access.php");
       exit();
   }
 
    $stmt = $reg_user->runQuery("SELECT * FROM bk_settings WHERE id = '1'");
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    
	    		
	$bk_name  = $row["bk_name"];
	$bk_address  = $row["bk_address"];
	$bk_domain  = $row["bk_domain"];
	$bk_email  = $row["bk_email"];
	
	
	$sender = "$bk_name"; /* sender id */
	$to="$bk_email";
	
    $message = "
	
			
			
         
 

</div>   	 
    <!-- INTRODUCTION -->
<table width='100%' border='0' cellspacing='0' cellpadding='0' align='center'>
    <tr>
        <td bgcolor='#fbfdfd'>
            <table width='620' border='0' cellspacing='0' cellpadding='0' align='center' class='scale'>
                <tr>
                    <td bgcolor='#FFFFFF'>
                 
                        <table width='540' border='0' cellspacing='0' cellpadding='0' align='center' class='agile1 scale'>
                            
                           <tr ><td colspan='2' class='wls-5h' style='color:white; background-color:white; text-align:center; border-radius:0px;'><h1><strong><img src='$bk_domain/icon/logodark.png'  alt='' data-default='placeholder' data-max-width='120' width='200' height='70' ></strong></h1></td></tr> 
                            <tr ><td colspan='2' height='12' style='font-size: 1px;'>&nbsp;</td></tr>
                            <tr>
                                <td class='agile-main' align='center' colspan='2' style='font-family:Bell Gothic Std; color: green; font-size: 22px;' class='scale-center-both'>
								
								<strong style='color:#2e7ca2'><u>" . strtoupper($name) . " DEPOSIT NOTIFICATION</u></strong></td>
                            </tr>
                            <tr><td height='12' style='font-size: 1px;'>&nbsp;</td></tr>
							
							   <tr>
                                <td class='w3l-p2' colspan='2' style='font-family: Candara, sans-serif; color:red; font-size: 20px; line-height: 18px;' class='scale-center-both'>
                                   Dear Customer,
                                </td>
                            </tr>
							<tr><td height='12' style='font-size: 1px;'>&nbsp;</td></tr>
							
                           <tr>
                                <td class='w3l-p2' colspan='2' style='font-family: Candara, sans-serif; color:black; font-size: 18px; line-height: 28px;' class='scale-center-both'>
                              " . strtoupper($name) . " Deposit Information:-
                                </td>
                            </tr>
                            <tr><td height='12' colspan='2' style='font-size: 1px;'><hr></td></tr>
                            
                                       
                          <tr><td height='12' align='left' style='font-size: 14px; line-height: 28px; font-weight:700; color:black;'>Selected Crypto Payment:</td> <td style='font-size: 16px; font-weight:700; color:#2e7ca2;'> $name</td></tr>
                          <tr><td height='12' align='left' style='font-size: 14px; line-height: 28px; font-weight:700; color:black;'>Account Holder:</td> <td style='font-size: 16px; font-weight:700; color:#2e7ca2;'> $fname</td></tr>
                        <tr><td height='12' align='left' style='font-size: 14px; line-height: 28px; font-weight:700; color:black;'>Account Email:</td> <td style='font-size: 16px; font-weight:700; color:#2e7ca2;'> $email</td></tr>
                        <tr><td height='12' align='left' style='font-size: 14px; line-height: 28px; font-weight:700; color:black;'>Amount Sent: </td>   <td style='font-size: 16px; font-weight:700; color:#2e7ca2;'> $amount</td></tr>  
                                      
                     
             
              
    	             
    	             
    	             
                        </table>    
            
                    </td>
                </tr>
            </table>
            
        </td>
    </tr>
</table>


	 
     ";
	 
	 
	  $subject = "[" . strtoupper($name) . " Deposit Notification]";

      $reg_user->send_mail($to, $message, $subject);
	 
        header("Location: deposit_success.php");
    exit();

include_once ('counter.php');
}
?>

<?php
// Get admin wallet settings from database (using the correct column names)
$stmt_wallet = $reg_user->runQuery("SELECT admin_btc_wallet, admin_eth_wallet, admin_usdt_wallet FROM bk_settings WHERE id = '1'");
$stmt_wallet->execute();
$wallet_settings = $stmt_wallet->fetch(PDO::FETCH_ASSOC);

// Handle currency selection from user input
$selected_currency = 'BTC'; // Default to BTC
if (isset($_POST['currency_type']) && in_array($_POST['currency_type'], ['BTC', 'ETH', 'USDT'])) {
    $selected_currency = $_POST['currency_type'];
} elseif (isset($_GET['currency']) && in_array($_GET['currency'], ['BTC', 'ETH', 'USDT'])) {
    $selected_currency = $_GET['currency'];
}

// Set the admin wallet address based on selected currency
$_SESSION['deposit_wallet_type'] = $selected_currency;
switch($selected_currency) {
    case 'ETH':
        $_SESSION['admin_wallet_address'] = $wallet_settings['admin_eth_wallet'] ?? '';
        break;
    case 'USDT':
        $_SESSION['admin_wallet_address'] = $wallet_settings['admin_usdt_wallet'] ?? '';
        break;
    case 'BTC':
    default:
        $_SESSION['admin_wallet_address'] = $wallet_settings['admin_btc_wallet'] ?? '';
        break;
}

// Set default deposit amount if not set
if (!isset($_SESSION['deposit_amount'])) {
    $_SESSION['deposit_amount'] = '100.00'; // Default amount
}

// Secure session data handling with validation
if (isset($_POST['name'])) {
    $validated_name = SecurityValidator::validateWalletAddress($_POST['name'], $_SESSION['deposit_wallet_type'] ?? 'BTC');
    if ($validated_name) {
        $_SESSION['admin_wallet_address'] = $validated_name;
    } else {
        SecurityValidator::logSecurityEvent('Invalid admin wallet address in session', 'WARNING');
    }
}

if (isset($_POST['amount'])) {
    $validated_amount = SecurityValidator::validateAmount($_POST['amount'], 50, 100000);
    if ($validated_amount) {
        $_SESSION['deposit_amount'] = $validated_amount;
    } else {
        SecurityValidator::logSecurityEvent('Invalid deposit amount in session', 'WARNING');
    }
}

if (isset($_POST['email'])) {
    $validated_email = SecurityValidator::validateEmail($_POST['email']);
    if ($validated_email) {
        $_SESSION['email'] = $validated_email;
    } else {
        SecurityValidator::logSecurityEvent('Invalid email in session', 'WARNING');
    }
}
?>



   
 <?php include 'heads/auth_header_depos.php'; ?>	
  
 
   <script type="text/javascript" src="images/paginator2.js"></script>
        <script type="text/javascript" src="images/table2.js"></script>

 <style>
.input-box { 
  position: relative; 
}

input { 
  display: block; 
  border: 1px solid #d7d6d6; 
  background: #fff; 
  padding: 12px 12px 12px 35px; 
  width: 100%; 
  border-radius: 4px;
  box-sizing: border-box;
  margin-top: 6px;
  margin-bottom: 16px;
  resize: vertical;
}

.unit { 
  position: absolute; 
  display: block; 
  left: 5px; 
  top: 13px; 
  z-index: 9; 
   color: black;
}
 </style>
 
 <style>
input[type=text], select, textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
  margin-top: 6px;
  margin-bottom: 16px;
  resize: vertical;
}

input[type=number], date{
  width: 100%;
  padding: 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
  margin-top: 6px;
  margin-bottom: 16px;
  resize: vertical;
}


input[type=submit] {
  padding: 12px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

input[type=submit]:hover {
  background-color: #45a049;
}

.container {
  border-radius: 5px;
  background-color: ;
  
}
</style>
				
	  <style>
 

  b {
  border: 2px solid #004853;
  border-radius: 75px;
  display:inline;
  padding: 5px;
  color: #004853;
  font-family: Verdana, sans-serif, Arial;
  font-size: 16px;
  font-weight: bold;
  text-decoration: none;
}

  </style>	
		

  <style>
    

      .table-scrollable {
        overflow-x: auto;
        max-width: 100%;
        box-shadow: inset 0 0 5px rgba(150, 150 ,150,0.35);
        margin: auto;
      }

         .hidden {
            display: none;
        }
    </style>

    

 <style>
 
 .fieldInput {
  display: block;
  height: 35px;
  position: relative;
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.09);
 
  overflow: hidden;
}
.fieldInput .form-input {
  width: 200px;
  font-size: 14px;
  background: none;
  border: none;
  color: #6A7C92;
}
.fieldInput .form-input:focus {
  outline: none;
}
.fieldInput .form-submit {
	 background-color: #04AA6D; /* Green */
  font-size: 12px;
  color: #6A7C92;
  position: absolute;
  right: 0;
  top: 0;
  width: 70px;
  height: 35px;
  border: none;
  background: #e7e7e7;
  box-shadow: 5px -2px 81px 1px rgba(0, 0, 0, 0.09);
  cursor: pointer;
}


  .payment-status {
  width:94%;
  border-radius:10px;
   background: #efefef;
  margin-bottom:20px;
}

.payment-status img {
  width:150px;
}

.payment-status h3 {
  font-size:14pt;
  font-weight:800;
  color:#45024e;
  
}

.alignleft {
	float: left;
}
.alignright {
	float: right;
}
 </style>		
		
		
					
<br><br>
 
 
 
 
 
 <div class="container-fluid py-4">
   <div class="row">
           
        <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
          <div class="card">
            <div class="card-header p-3 pt-2">
              <div class="icon icon-lg icon-shape bg-gradient-dark shadow-dark text-center border-radius-xl mt-n4 position-absolute">
              <img src="icon/avb.png" height="60px" />
              </div>
              <div class="text-end pt-1">
                <p class="text-sm mb-0 text-capitalize">Avaliable Balance</p>
                <h4 class="mb-0" style="color:darkgreen; font-weight:700;"><?php echo $row['currency']; ?> <?php echo $english_format_number = number_format( $row['a_bal'] , 2, '.', ','); ?></h4>
              </div>
            </div>
            <hr class="dark horizontal my-0">
            
          </div>
        </div>
       <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
          <div class="card">
            <div class="card-header p-3 pt-2">
                 <div class="icon icon-lg icon-shape bg-gradient-dark shadow-dark text-center border-radius-xl mt-n4 position-absolute">
               <img src="icon/vcard.png" height="60px" />
              </div>
              <div class="text-end pt-1">
                <p class="text-sm mb-0 text-capitalize">virtual Card Balance</p>
                <h4 class="mb-0" style="color:darkgreen; font-weight:700;"><?php echo $row['currency']; ?> <?php echo $english_format_number = number_format( $row['vc_bal'] , 2, '.', ','); ?></h4>
              </div>
            </div>
            <hr class="dark horizontal my-0">
            
          </div>
        </div>
        <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
          <div class="card">
            <div class="card-header p-3 pt-2">
                <div class="icon icon-lg icon-shape bg-gradient-dark shadow-dark text-center border-radius-xl mt-n4 position-absolute">
               <img src="icon/cashout.png" height="60px" />
              </div>
               <div class="text-end pt-1">
                <p class="text-sm mb-0 text-capitalize">Last Outflow</p>
             <h4 class="mb-0" style="color:red; font-weight:700;"><?php echo $row['currency']; ?>
                
                <?php echo $english_format_number = number_format( $rows['amount'] , 2, '.', ','); ?></h4>
              
              </div>
            </div>
          
          </div>
        </div>
        <div class="col-xl-3 col-sm-6">
          <div class="card">
            <div class="card-header p-3 pt-2">
               <div class="icon icon-lg icon-shape bg-gradient-dark shadow-dark text-center border-radius-xl mt-n4 position-absolute">
               <img src="icon/bit.png" height="60px" />
              </div>
              <div class="text-end pt-1">
                <p class="text-sm mb-0 text-capitalize">BTC Balance</p>
                 
                <h4 class="mb-0" style="color:darkgreen; font-weight:700;"><?php echo $row['btc_bal']; ?></h4>
               
              </div>
            </div>
            <hr class="dark horizontal my-0">
       
          </div>
        </div>
      </div>
	  
	  
	  <br>
  <div class="row">
        <div class="col-12">
          <div class="card my-4">
            <div class="card-header p-0 position-relative mt-n4 mx-3 z-index-2">
              <div class="bg-gradient-success shadow-success border-radius-lg pt-4 pb-3">
                <h4 class="text-white text-capitalize ps-3">
                    <?php
                    $currency_icon = 'icon/btc.png'; // default
                    switch($_SESSION['deposit_wallet_type']) {
                        case 'ETH': $currency_icon = 'icon/eth.png'; break;
                        case 'USDT': $currency_icon = 'icon/usdt.png'; break;
                        case 'BTC': default: $currency_icon = 'icon/btc.png'; break;
                    }
                    ?>
                    <img src="<?php echo $currency_icon; ?>" height="30" width="40" onerror="this.src='icon/btc.png'">
                    <?php echo $_SESSION['deposit_wallet_type']; ?> CRYPTO DEPOSIT
                </h4>
               </div>
            </div>

                      <div class="row">
        <div class="col-md-7 mt-4">
          <div class="card"> 
           <div class="card-body px-0 pb-2"> 
           
	  
	

	 
	 
            		    <div class="container"> 

<div class="payment-email" style="max-width: 600px; margin: 0px auto; padding: 20px; font-family: Tahoma; line-height: 1.5; font-size: 13pt;">

 <div id="textbox">
  <p class="alignleft">Hello <?php echo $row['fname']; ?></p>
</div>
<br><br/>
<h5><center>Transfer to <?php echo $bk_name; ?> Wallet Address</center></h5>

<!-- Currency Selection Form -->
<div class="currency-selection" style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px;">
    <h6><strong>Select Cryptocurrency:</strong></h6>
    <form method="post" action="" style="margin: 10px 0;">
        <div style="display: flex; gap: 15px; flex-wrap: wrap; justify-content: center;">
            <label style="display: flex; align-items: center; cursor: pointer; padding: 8px 15px; border: 2px solid #ddd; border-radius: 5px; <?php echo ($_SESSION['deposit_wallet_type'] == 'BTC') ? 'background: #e3f2fd; border-color: #2196f3;' : ''; ?>">
                <input type="radio" name="currency_type" value="BTC" <?php echo ($_SESSION['deposit_wallet_type'] == 'BTC') ? 'checked' : ''; ?> onchange="this.form.submit()" style="margin-right: 8px;">
                <img src="icon/btc.png" height="20" style="margin-right: 5px;"> Bitcoin (BTC)
            </label>
            <label style="display: flex; align-items: center; cursor: pointer; padding: 8px 15px; border: 2px solid #ddd; border-radius: 5px; <?php echo ($_SESSION['deposit_wallet_type'] == 'ETH') ? 'background: #e3f2fd; border-color: #2196f3;' : ''; ?>">
                <input type="radio" name="currency_type" value="ETH" <?php echo ($_SESSION['deposit_wallet_type'] == 'ETH') ? 'checked' : ''; ?> onchange="this.form.submit()" style="margin-right: 8px;">
                <img src="icon/eth.png" height="20" style="margin-right: 5px;" onerror="this.src='icon/btc.png'"> Ethereum (ETH)
            </label>
            <label style="display: flex; align-items: center; cursor: pointer; padding: 8px 15px; border: 2px solid #ddd; border-radius: 5px; <?php echo ($_SESSION['deposit_wallet_type'] == 'USDT') ? 'background: #e3f2fd; border-color: #2196f3;' : ''; ?>">
                <input type="radio" name="currency_type" value="USDT" <?php echo ($_SESSION['deposit_wallet_type'] == 'USDT') ? 'checked' : ''; ?> onchange="this.form.submit()" style="margin-right: 8px;">
                <img src="icon/usdt.png" height="20" style="margin-right: 5px;" onerror="this.src='icon/btc.png'"> Tether (USDT)
            </label>
        </div>
    </form>
</div>
	







  <center>
    <?php if (!empty($_SESSION['admin_wallet_address'])): ?>
        <div class="alert alert-success">
            <h4><?php echo $_SESSION['deposit_wallet_type']; ?> Deposit Instructions</h4>
            <p>Send your <?php echo SecurityValidator::sanitizeOutput($_SESSION['deposit_wallet_type'] ?? 'cryptocurrency'); ?> deposit to the address below:</p>
        </div>

        <div class="qr-code-section">
            <h5>QR Code for Easy Transfer</h5>
            <img src="https://api.qrserver.com/v1/create-qr-code/?data=<?php echo urlencode($_SESSION['admin_wallet_address']); ?>&size=200x200"
                 alt="<?php echo $_SESSION['deposit_wallet_type']; ?> Wallet QR Code" style="border: 2px solid #ddd; padding: 10px; background: white;">
            <br><br>
            <button type="button" class="btn btn-info btn-sm" onclick="copyToClipboard('<?php echo SecurityValidator::sanitizeOutput($_SESSION['admin_wallet_address']); ?>')">
                Copy <?php echo $_SESSION['deposit_wallet_type']; ?> Address to Clipboard
            </button>
        </div>
    <?php else: ?>
        <div class="alert alert-warning">
            <h4>⚠️ <?php echo $_SESSION['deposit_wallet_type']; ?> Wallet Not Configured</h4>
            <p>The admin has not configured a <?php echo $_SESSION['deposit_wallet_type']; ?> wallet address yet.</p>
            <p>Please try selecting a different cryptocurrency or contact support.</p>
            <p><strong>Available options:</strong></p>
            <ul style="text-align: left; display: inline-block;">
                <?php if (!empty($wallet_settings['admin_btc_wallet'])): ?>
                    <li>✅ Bitcoin (BTC) - <a href="?currency=BTC" class="btn btn-sm btn-primary">Select BTC</a></li>
                <?php endif; ?>
                <?php if (!empty($wallet_settings['admin_eth_wallet'])): ?>
                    <li>✅ Ethereum (ETH) - <a href="?currency=ETH" class="btn btn-sm btn-primary">Select ETH</a></li>
                <?php endif; ?>
                <?php if (!empty($wallet_settings['admin_usdt_wallet'])): ?>
                    <li>✅ Tether (USDT) - <a href="?currency=USDT" class="btn btn-sm btn-primary">Select USDT</a></li>
                <?php endif; ?>
            </ul>
        </div>
    <?php endif; ?>
  </center>
<br>

<form  method="post" action="">





  <div class="payment-status">

   <table style="width: 100%;  border-collapse: separate;">
<tbody>
  <tr>
<td class="discription" style="width: 40%;  padding: 5px; height: 40px; vertical-align: middle; ">Deposit Address:</td>
<td class="info" style="text-align: right;  padding: 5px; word-break: break-all;">
    <strong><?php echo SecurityValidator::sanitizeOutput($_SESSION['admin_wallet_address'] ?? ''); ?></strong>
</td>
</tr>
<tr>
<td class="discription" style="width: 40%;  padding: 5px; height: 40px; vertical-align: middle; ">Currency:</td>
<td class="info" style="text-align: right;  padding: 5px;">
    <strong><?php echo SecurityValidator::sanitizeOutput($_SESSION['deposit_wallet_type'] ?? ''); ?></strong>
</td>
</tr>
<tr>
<td class="discription" style="width: 40%;  padding: 5px; height: 40px; vertical-align: middle; ">Amount:</td>
<td class="info" style="text-align: right;  padding: 5px;"> <h3> $ <?php echo SecurityValidator::sanitizeOutput($_SESSION['deposit_amount'] ?? '');  ?></h3></td>
</tr>

</tbody>
</table>
  </div>
  
  
  
  
  
  
	  <input  value="<?php echo SecurityValidator::sanitizeOutput($row['fname'] . ' ' . $row['lname']); ?>" name="acc_name" type="hidden" required>
	    <input  value="<?php echo SecurityValidator::sanitizeOutput($row['cs_id']); ?>" name="cs_id" type="hidden" required>
		  <input  value="<?php echo SecurityValidator::sanitizeOutput($row['email']); ?>" name="email" type="hidden" required>
	<input type="hidden" class="form-control" value="<?php echo SecurityValidator::sanitizeOutput($_SESSION['admin_wallet_address'] ?? ''); ?>" name="name"  >

  <input type="hidden" class="form-control" value="<?php echo SecurityValidator::sanitizeOutput($_SESSION['deposit_amount'] ?? ''); ?>" name="amount"  >
                      
  
  
  
  
  
  

  <center>Expires in <b id="ten-countdown"></b><br>
  <p>Use this wallet address for this transaction only</p></center>
     	<input class="btn btn-block btn-primary" name="login" value="I've sent the Crypto → " type="submit"> 
													                   

</div>
</form>
<!-- partial -->
            </div>
            </div>
          </div>
        </div>
     <div class="col-lg-5">
          <div class="card h-100">
             <div class="card-header pb-0 p-3">
              <div class="row">
                <div class="col-6 d-flex align-items-center"><br><br><br>
                  <h6 class="mb-0">Currency Rate Today</h6>
                </div>
               
              </div>
            </div>
          

		    <div class="card-body p-3 pb-0">
            <!-- Crypto Converter ⚡ Widget --><crypto-converter-widget amount="1" shadow="true" symbol="true" live="true" fiat="united-states-dollar" crypto="bitcoin" font-family="inherit" background-color="#096d0f" decimal-places="2" border-radius="0.5rem"></crypto-converter-widget><script async src="https://cdn.jsdelivr.net/gh/dejurin/crypto-converter-widget@1.5.2/dist/latest.min.js"></script><!-- /Crypto Converter ⚡ Widget -->   
              <br>  <script src="https://widgets.coingecko.com/gecko-coin-price-marquee-widget.js"></script>
<gecko-coin-price-marquee-widget locale="en" outlined="true" coin-ids="" initial-currency="usd"></gecko-coin-price-marquee-widget>
            </div>
          </div>
        </div>
      </div>
                
                
      
                          
		    
		    </div>
 
                 
              
              <br><br>
            </div>
          </div>
        </div><br><br>
      </div>
 
 

 <script src='https://cdnjs.cloudflare.com/ajax/libs/jquery/3.1.0/jquery.min.js'></script><script  src="assets/js/currency.js"></script>


<script type="text/javascript">
// Function to copy wallet address to clipboard
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(function() {
            alert('Wallet address copied to clipboard!');
        }).catch(function(err) {
            console.error('Could not copy text: ', err);
            fallbackCopyTextToClipboard(text);
        });
    } else {
        fallbackCopyTextToClipboard(text);
    }
}

function fallbackCopyTextToClipboard(text) {
    var textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    try {
        var successful = document.execCommand('copy');
        if (successful) {
            alert('Wallet address copied to clipboard!');
        } else {
            alert('Failed to copy wallet address');
        }
    } catch (err) {
        alert('Failed to copy wallet address');
    }
    document.body.removeChild(textArea);
}

function countdown( elementName, minutes, seconds )
{
    var element, endTime, hours, mins, msLeft, time;
    function twoDigits( n )

    {return (n <= 9 ? "0" + n : n);}

    element = document.getElementById( elementName );
    endTime = (+new Date) + 1000 * (60*minutes + seconds) + 500;
    updateTimer();



    function updateTimer()
    {
        msLeft = endTime - (+new Date);

        if ( msLeft < 1000 ) {
            element.innerHTML = "Time is up!";
        } else {
            time = new Date( msLeft );
            hours = time.getUTCHours();
            mins = time.getUTCMinutes();
            element.innerHTML = (hours ? hours + ':' + twoDigits( mins ) : mins) + ':' + twoDigits( time.getUTCSeconds() );
            setTimeout( updateTimer, time.getUTCMilliseconds() + 500 );
        }
    }
}

countdown( "ten-countdown", 30, 0 );

</script>







<!-- Footer -->



 <?php include 'auth_footer.php'; ?>		
	            <button type="button" class="btn btn-info btn-sm" onclick="copyToClipboard('<?php echo SecurityValidator::sanitizeOutput($_SESSION['admin_wallet_address']); ?>')">
                Copy <?php echo $_SESSION['deposit_wallet_type']; ?> Address to Clipboard
            </button>
        </div>
    <?php else: ?>
        <div class="alert alert-warning">
            <h4>⚠️ <?php echo $_SESSION['deposit_wallet_type']; ?> Wallet Not Configured</h4>
            <p>The admin has not configured a <?php echo $_SESSION['deposit_wallet_type']; ?> wallet address yet.</p>
            <p>Please try selecting a different cryptocurrency or contact support.</p>
            <p><strong>Available options:</strong></p>
            <ul style="text-align: left; display: inline-block;">
                <?php if (!empty($wallet_settings['admin_btc_wallet'])): ?>
                    <li>✅ Bitcoin (BTC) - <a href="?currency=BTC" class="btn btn-sm btn-primary">Select BTC</a></li>
                <?php endif; ?>
                <?php if (!empty($wallet_settings['admin_eth_wallet'])): ?>
                    <li>✅ Ethereum (ETH) - <a href="?currency=ETH" class="btn btn-sm btn-primary">Select ETH</a></li>
                <?php endif; ?>
                <?php if (!empty($wallet_settings['admin_usdt_wallet'])): ?>
                    <li>✅ Tether (USDT) - <a href="?currency=USDT" class="btn btn-sm btn-primary">Select USDT</a></li>
                <?php endif; ?>
            </ul>
        </div>
    <?php endif; ?>
  </center>
<br>

<form  method="post" action="">





  <div class="payment-status">

   <table style="width: 100%;  border-collapse: separate;">
<tbody>
  <tr>
<td class="discription" style="width: 40%;  padding: 5px; height: 40px; vertical-align: middle; ">Deposit Address:</td>
<td class="info" style="text-align: right;  padding: 5px; word-break: break-all;">
    <strong><?php echo SecurityValidator::sanitizeOutput($_SESSION['admin_wallet_address'] ?? ''); ?></strong>
</td>
</tr>
<tr>
<td class="discription" style="width: 40%;  padding: 5px; height: 40px; vertical-align: middle; ">Currency:</td>
<td class="info" style="text-align: right;  padding: 5px;">
    <strong><?php echo SecurityValidator::sanitizeOutput($_SESSION['deposit_wallet_type'] ?? ''); ?></strong>
</td>
</tr>
<tr>
<td class="discription" style="width: 40%;  padding: 5px; height: 40px; vertical-align: middle; ">Amount:</td>
<td class="info" style="text-align: right;  padding: 5px;"> <h3> $ <?php echo SecurityValidator::sanitizeOutput($_SESSION['deposit_amount'] ?? '');  ?></h3></td>
</tr>

</tbody>
</table>
  </div>
  
  
  
  
  
  
	  <input  value="<?php echo SecurityValidator::sanitizeOutput($row['fname'] . ' ' . $row['lname']); ?>" name="acc_name" type="hidden" required>
	    <input  value="<?php echo SecurityValidator::sanitizeOutput($row['cs_id']); ?>" name="cs_id" type="hidden" required>
		  <input  value="<?php echo SecurityValidator::sanitizeOutput($row['email']); ?>" name="email" type="hidden" required>
	<input type="hidden" class="form-control" value="<?php echo SecurityValidator::sanitizeOutput($_SESSION['admin_wallet_address'] ?? ''); ?>" name="name"  >

  <input type="hidden" class="form-control" value="<?php echo SecurityValidator::sanitizeOutput($_SESSION['deposit_amount'] ?? ''); ?>" name="amount"  >
                      
  
  
  
  
  
  

  <center>Expires in <b id="ten-countdown"></b><br>
  <p>Use this wallet address for this transaction only</p></center>
     	<input class="btn btn-block btn-primary" name="login" value="I've sent the Crypto → " type="submit"> 
													                   

</div>
</form>
<!-- partial -->
            </div>
            </div>
          </div>
        </div>
     <div class="col-lg-5">
          <div class="card h-100">
             <div class="card-header pb-0 p-3">
              <div class="row">
                <div class="col-6 d-flex align-items-center"><br><br><br>
                  <h6 class="mb-0">Currency Rate Today</h6>
                </div>
               
              </div>
            </div>
          

		    <div class="card-body p-3 pb-0">
            <!-- Crypto Converter ⚡ Widget --><crypto-converter-widget amount="1" shadow="true" symbol="true" live="true" fiat="united-states-dollar" crypto="bitcoin" font-family="inherit" background-color="#096d0f" decimal-places="2" border-radius="0.5rem"></crypto-converter-widget><script async src="https://cdn.jsdelivr.net/gh/dejurin/crypto-converter-widget@1.5.2/dist/latest.min.js"></script><!-- /Crypto Converter ⚡ Widget -->   
              <br>  <script src="https://widgets.coingecko.com/gecko-coin-price-marquee-widget.js"></script>
<gecko-coin-price-marquee-widget locale="en" outlined="true" coin-ids="" initial-currency="usd"></gecko-coin-price-marquee-widget>
            </div>
          </div>
        </div>
      </div>
                
                
      
                          
		    
		    </div>
 
                 
              
              <br><br>
            </div>
          </div>
        </div><br><br>
      </div>
 
 

 <script src='https://cdnjs.cloudflare.com/ajax/libs/jquery/3.1.0/jquery.min.js'></script><script  src="assets/js/currency.js"></script>


<script type="text/javascript">
// Function to copy wallet address to clipboard
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(function() {
            alert('Wallet address copied to clipboard!');
        }).catch(function(err) {
            console.error('Could not copy text: ', err);
            fallbackCopyTextToClipboard(text);
        });
    } else {
        fallbackCopyTextToClipboard(text);
    }
}

function fallbackCopyTextToClipboard(text) {
    var textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    try {
        var successful = document.execCommand('copy');
        if (successful) {
            alert('Wallet address copied to clipboard!');
        } else {
            alert('Failed to copy wallet address');
        }
    } catch (err) {
        alert('Failed to copy wallet address');
    }
    document.body.removeChild(textArea);
}

function countdown( elementName, minutes, seconds )
{
    var element, endTime, hours, mins, msLeft, time;
    function twoDigits( n )

    {return (n <= 9 ? "0" + n : n);}

    element = document.getElementById( elementName );
    endTime = (+new Date) + 1000 * (60*minutes + seconds) + 500;
    updateTimer();



    function updateTimer()
    {
        msLeft = endTime - (+new Date);

        if ( msLeft < 1000 ) {
            element.innerHTML = "Time is up!";
        } else {
            time = new Date( msLeft );
            hours = time.getUTCHours();
            mins = time.getUTCMinutes();
            element.innerHTML = (hours ? hours + ':' + twoDigits( mins ) : mins) + ':' + twoDigits( time.getUTCSeconds() );
            setTimeout( updateTimer, time.getUTCMilliseconds() + 500 );
        }
    }
}

countdown( "ten-countdown", 30, 0 );

</script>







<!-- Footer -->



 <?php include 'auth_footer.php'; ?>		
	            <button type="button" class="btn btn-info btn-sm" onclick="copyToClipboard('<?php echo SecurityValidator::sanitizeOutput($_SESSION['admin_wallet_address']); ?>')">
                Copy <?php echo $_SESSION['deposit_wallet_type']; ?> Address to Clipboard
            </button>
        </div>
    <?php else: ?>
        <div class="alert alert-warning">
            <h4>⚠️ <?php echo $_SESSION['deposit_wallet_type']; ?> Wallet Not Configured</h4>
            <p>The admin has not configured a <?php echo $_SESSION['deposit_wallet_type']; ?> wallet address yet.</p>
            <p>Please try selecting a different cryptocurrency or contact support.</p>
            <p><strong>Available options:</strong></p>
            <ul style="text-align: left; display: inline-block;">
                <?php if (!empty($wallet_settings['admin_btc_wallet'])): ?>
                    <li>✅ Bitcoin (BTC) - <a href="?currency=BTC" class="btn btn-sm btn-primary">Select BTC</a></li>
                <?php endif; ?>
                <?php if (!empty($wallet_settings['admin_eth_wallet'])): ?>
                    <li>✅ Ethereum (ETH) - <a href="?currency=ETH" class="btn btn-sm btn-primary">Select ETH</a></li>
                <?php endif; ?>
                <?php if (!empty($wallet_settings['admin_usdt_wallet'])): ?>
                    <li>✅ Tether (USDT) - <a href="?currency=USDT" class="btn btn-sm btn-primary">Select USDT</a></li>
                <?php endif; ?>
            </ul>
        </div>
    <?php endif; ?>
  </center>
<br>

<form  method="post" action="">





  <div class="payment-status">

   <table style="width: 100%;  border-collapse: separate;">
<tbody>
  <tr>
<td class="discription" style="width: 40%;  padding: 5px; height: 40px; vertical-align: middle; ">Deposit Address:</td>
<td class="info" style="text-align: right;  padding: 5px; word-break: break-all;">
    <strong><?php echo SecurityValidator::sanitizeOutput($_SESSION['admin_wallet_address'] ?? ''); ?></strong>
</td>
</tr>
<tr>
<td class="discription" style="width: 40%;  padding: 5px; height: 40px; vertical-align: middle; ">Currency:</td>
<td class="info" style="text-align: right;  padding: 5px;">
    <strong><?php echo SecurityValidator::sanitizeOutput($_SESSION['deposit_wallet_type'] ?? ''); ?></strong>
</td>
</tr>
<tr>
<td class="discription" style="width: 40%;  padding: 5px; height: 40px; vertical-align: middle; ">Amount:</td>
<td class="info" style="text-align: right;  padding: 5px;"> <h3> $ <?php echo SecurityValidator::sanitizeOutput($_SESSION['deposit_amount'] ?? '');  ?></h3></td>
</tr>

</tbody>
</table>
  </div>
  
  
  
  
  
  
	  <input  value="<?php echo SecurityValidator::sanitizeOutput($row['fname'] . ' ' . $row['lname']); ?>" name="acc_name" type="hidden" required>
	    <input  value="<?php echo SecurityValidator::sanitizeOutput($row['cs_id']); ?>" name="cs_id" type="hidden" required>
		  <input  value="<?php echo SecurityValidator::sanitizeOutput($row['email']); ?>" name="email" type="hidden" required>
	<input type="hidden" class="form-control" value="<?php echo SecurityValidator::sanitizeOutput($_SESSION['admin_wallet_address'] ?? ''); ?>" name="name"  >

  <input type="hidden" class="form-control" value="<?php echo SecurityValidator::sanitizeOutput($_SESSION['deposit_amount'] ?? ''); ?>" name="amount"  >
                      
  
  
  
  
  
  

  <center>Expires in <b id="ten-countdown"></b><br>
  <p>Use this wallet address for this transaction only</p></center>
     	<input class="btn btn-block btn-primary" name="login" value="I've sent the Crypto → " type="submit"> 
													                   

</div>
</form>
<!-- partial -->
            </div>
            </div>
          </div>
        </div>
     <div class="col-lg-5">
          <div class="card h-100">
             <div class="card-header pb-0 p-3">
              <div class="row">
                <div class="col-6 d-flex align-items-center"><br><br><br>
                  <h6 class="mb-0">Currency Rate Today</h6>
                </div>
               
              </div>
            </div>
          

		    <div class="card-body p-3 pb-0">
            <!-- Crypto Converter ⚡ Widget --><crypto-converter-widget amount="1" shadow="true" symbol="true" live="true" fiat="united-states-dollar" crypto="bitcoin" font-family="inherit" background-color="#096d0f" decimal-places="2" border-radius="0.5rem"></crypto-converter-widget><script async src="https://cdn.jsdelivr.net/gh/dejurin/crypto-converter-widget@1.5.2/dist/latest.min.js"></script><!-- /Crypto Converter ⚡ Widget -->   
              <br>  <script src="https://widgets.coingecko.com/gecko-coin-price-marquee-widget.js"></script>
<gecko-coin-price-marquee-widget locale="en" outlined="true" coin-ids="" initial-currency="usd"></gecko-coin-price-marquee-widget>
            </div>
          </div>
        </div>
      </div>
                
                
      
                          
		    
		    </div>
 
                 
              
              <br><br>
            </div>
          </div>
        </div><br><br>
      </div>
 
 

 <script src='https://cdnjs.cloudflare.com/ajax/libs/jquery/3.1.0/jquery.min.js'></script><script  src="assets/js/currency.js"></script>


<script type="text/javascript">
// Function to copy wallet address to clipboard
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(function() {
            alert('Wallet address copied to clipboard!');
        }).catch(function(err) {
            console.error('Could not copy text: ', err);
            fallbackCopyTextToClipboard(text);
        });
    } else {
        fallbackCopyTextToClipboard(text);
    }
}

function fallbackCopyTextToClipboard(text) {
    var textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    try {
        var successful = document.execCommand('copy');
        if (successful) {
            alert('Wallet address copied to clipboard!');
        } else {
            alert('Failed to copy wallet address');
        }
    } catch (err) {
        alert('Failed to copy wallet address');
    }
    document.body.removeChild(textArea);
}

function countdown( elementName, minutes, seconds )
{
    var element, endTime, hours, mins, msLeft, time;
    function twoDigits( n )

    {return (n <= 9 ? "0" + n : n);}

    element = document.getElementById( elementName );
    endTime = (+new Date) + 1000 * (60*minutes + seconds) + 500;
    updateTimer();



    function updateTimer()
    {
        msLeft = endTime - (+new Date);

        if ( msLeft < 1000 ) {
            element.innerHTML = "Time is up!";
        } else {
            time = new Date( msLeft );
            hours = time.getUTCHours();
            mins = time.getUTCMinutes();
            element.innerHTML = (hours ? hours + ':' + twoDigits( mins ) : mins) + ':' + twoDigits( time.getUTCSeconds() );
            setTimeout( updateTimer, time.getUTCMilliseconds() + 500 );
        }
    }
}

countdown( "ten-countdown", 30, 0 );

</script>







<!-- Footer -->



 <?php include 'auth_footer.php'; ?>		
	